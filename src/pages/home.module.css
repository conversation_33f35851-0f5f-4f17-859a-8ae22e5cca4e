.app {
  width: 100vw;
  min-height: 100vh;
  padding: 0 16px;
  box-sizing: border-box;
  max-width: 1104px;
  /* 1072+16*2 */
  min-width: 848px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header {
  width: 100%;
  height: 8vh;
}

.headerText {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #000000eb;
  text-align: center;
  width: 36px;
}

.logoSection {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
  height: 192px;
}

.logoWrapper {
  width: 106px;
  height: 56px;
}

.logo {
  width: 100%;
  height: 100%;
  color: var(--tb-brand-light-color, #ff5000);
  transition: color 0.3s ease;
}

.searchContainer {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;
  height: 104px;
  margin-top: 32px;
}

.middleHeader {
  width: 100%;
  height: 6vh;
}

.recommendBannerContainer {
  position: fixed;
  left: 0;
  bottom: 16px;
  width: 100%;
  z-index: 9;
}