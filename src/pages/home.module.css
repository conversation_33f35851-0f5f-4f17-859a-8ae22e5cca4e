.app {
  width: 100vw;
  min-height: 100vh;
  padding: 0 16px;
  box-sizing: border-box;
  max-width: 1104px;
  /* 1072+16*2 */
  min-width: 848px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header {
  width: 100%;
  height: 8vh;
}

.headerText {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #000000eb;
  text-align: center;
  width: 36px;
}

.logoSection {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
  height: 192px;
}

.logoWrapper {
  /* width: 280px;
  height: 84px;
  transition: all 0.3s ease; */
}

.logo {
  width: 296px;
  height: 100px;
  background-image: url('../assets/tb_client_logo_light_7_28.gif');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

@media (prefers-color-scheme: dark) {
  .logo {
    background-image: url('../assets/tb_client_logo_dark_7_28.gif');
  }
}

.searchContainer {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;
  height: 104px;
  margin-top: 10px;
}

.middleHeader {
  width: 100%;
  height: 6vh;
}

.recommendBannerContainer {
  position: fixed;
  left: 0;
  bottom: 16px;
  width: 100%;
  z-index: 9;
}