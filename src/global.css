body {
  background-color: #fff;
  --bg-color: #fff;
  --bg-light-color: #f5f5f5;
  --bg-light2-color: #f7f7f7;
  --bg-pop-color: #fff;
  --bg-tip-line: #ffeee6;
  --bg-white-color: rgba(0, 0, 0, 0.8);
  --bg-white-hover-color: rgba(0, 0, 0, 0.88);
  --tab-color: #11192d;
  --border-color: #ebebeb;
  --border-light-color: #fff;
  --primary-color: #1f1f1f;
  --secondary-color: #7a7a7a;
  --tertiary-color: #999;
  --disable-color: #ccc;
  --desc-color: rgba(0, 0, 0, 0.52);
  --img-invert: none;
  --ant-message-content-bg: #fff;
  --tb-brand-light-color: #ff5000;
  --tbpc-primary-color: rgba(0, 0, 0, 0.92);
  --tbpc-border-color: rgba(0, 0, 0, 0.08);
  --tbpc-bg-color: rgba(0, 0, 0, 0.04);
  --tbpc-info-bg-color: radial-gradient(142% 156% at 12% 0%, #FDF4E9 0%, rgba(253, 244, 233, 0) 100%), #FFE8C7;
  --tbpc-vip-bg-image: url(https://img.alicdn.com/imgextra/i1/6000000005107/O1CN01eAvtcz1nb3lhpQ1ZV_!!6000000005107-2-gg_dtc.png);
  --tbpc-btn-bg: #FFFCFA;
  --tbpc-btn-border-color: rgba(128, 73, 0, 0.08);
  --tbpc-btn-bg2: rgba(255, 255, 255, 0.5);
  --tbpc-btn-border-color2: rgba(0, 0, 0, 0.08);
  --tbpc-btn-bg2-hover:linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), rgba(255, 255, 255, 0.5);
  --tbpc-btn-bg2-active:linear-gradient(0deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.08)), rgba(255, 255, 255, 0.5);
  --tbpc-btn-border-color2-hover:rgba(0, 0, 0, 0.08);
  --tbpc-btn-border-color2-active:rgba(0, 0, 0, 0.08);
  --tbpc-img-bg: rgba(255, 255, 255, 0.48);
  --tbpc-search-bg-color: #f3f6f8;
  --tbpc-non-vip-bg: #FFF1EB;
  --tbpc-vip-color:#352A20;
  --tbpc-vip-icon: url(https://img.alicdn.com/imgextra/i1/O1CN01OY5LDQ1QrKgGtUfVZ_!!6000000002029-2-tps-72-32.png);
  --tbpc-client-hover-color: #ff5000;
  --tbpc-client-active-color: #C23D00;
  --tbpc-btn-bg-hover: linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #FFFCFA;
  --tbpc-btn-bg-active: linear-gradient(0deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.08)), #FFFCFA;
  --tbpc-btn-border-color-hover: rgba(128, 58, 26, 0.08);
  --tbpc-btn-border-color-active: rgba(128, 58, 26, 0.08);
  --tbpc-btn-bg-vip: #FEF5EA;
  --tbpc-btn-border-color-vip: rgba(128, 73, 0, 0.08);
  --tbpc-btn-bg-hover-vip: linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #FEF5EA;
  --tbpc-btn-bg-active-vip: linear-gradient(0deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.08)), #FEF5EA;
  --tbpc-btn-border-color-hover-vip: rgba(128, 73, 0, 0.08);
  --tbpc-btn-border-color-active-vip: rgba(128, 73, 0, 0.08);
  --tbpc-search-tag-bg: rgba(255, 80, 0, 0.082);
  --tbpc-loading-bg1: rgba(0, 0, 0, 0.03);
  --tbpc-loading-bg2: rgba(0, 0, 0, 0.06);
  --tbpc-search-icon-bg-hover: #EB4A00;
  --tbpc-search-icon-bg-active: #D64300;
  --tbpc-my-tao-backup-icon: url(https://img.alicdn.com/imgextra/i3/O1CN01Gjggl11gfSGFcFlnH_!!6000000004169-2-tps-208-208.png);
  --bg-recommend-banner: #fff;
  --recommend-banner-border: rgba(0, 0, 0, 0.02);
  --recommend-banner-shadow: rgba(0, 0, 0, 0.12);

  --tbpc-home-more-box-shadow-color: rgba(173, 14, 54, 0.16);
  --tbpc-shopping-url: url(https://img.alicdn.com/imgextra/i2/O1CN01UKV8qx1kcUARlQRjk_!!6000000004704-2-tps-148-64.png);
  --tbpc-more-arrow-url: url(https://img.alicdn.com/imgextra/i3/O1CN01UOFS541RvHj11fNdD_!!6000000002173-2-tps-64-64.png);
  --tbpc-feedback-icon-image: url(https://img.alicdn.com/imgextra/i1/O1CN01dGQxnE1WutShXPR2J_!!6000000002849-2-tps-96-96.png);
  --tbpc-feedback-bg-color: #fff;
  --tbpc-feedback-bg-color-hover: #f5f5f5;
  --tbpc-feedback-bg-color-active: #ebebeb;
  --tbpc-feedback-shadow-color: rgba(0, 0, 0, 0.08);
  --tbpc-feedback-text-color: #1f1f1f;
}
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1f1f1f;
    --bg-color: #1f1f1f;
    --bg-light-color: #292929;
    --bg-light2-color: rgba(255, 255, 255, 0.08);
    --bg-pop-color: #292929;
    --bg-tip-line: rgba(255, 51, 51, 0.08);
    --bg-white-color: rgba(255, 255, 255, 0.96);
    --bg-white-hover-color: #fff;
    --tab-color: #f5f5f5;
    --border-color: rgba(255, 255, 255, 0.08);
    --border-light-color: #333;
    --primary-color: #f5f5f5;
    --secondary-color: #9c9c9c;
    --tertiary-color: #9c9c9c;
    --disable-color: #5c5c5c;
    --desc-color: rgba(255, 255, 255, 0.56);
    --img-invert: invert(1);
    --ant-message-content-bg: #1f1f1f;
    --tb-brand-light-color: #ff590d;
    --tbpc-primary-color: rgba(255, 255, 255, 0.96);
    --tbpc-border-color: rgba(255, 255, 255, 0.08);
    --tbpc-bg-color: rgba(255, 255, 255, 0.04);
    --tbpc-info-bg-color: radial-gradient(142% 156% at 12% 0%, rgba(153, 127, 77, 0.4) 0%, rgba(56, 48, 39, 0) 100%), #664729;
    --tbpc-vip-bg-image: url(https://gw.alicdn.com/imgextra/i1/O1CN01mBmTY61TITNfKV9Bb_!!6000000002359-2-tps-584-205.png);
    --tbpc-btn-bg: rgba(255, 241, 235, 0.08);
    --tbpc-btn-border-color: rgba(255, 255, 255, 0.08);
    --tbpc-btn-bg2: rgba(255, 255, 255, 0.04);
    --tbpc-btn-border-color2: rgba(255, 255, 255, 0.08);
    --tbpc-btn-bg2-hover:rgba(255, 255, 255, 0.12);
    --tbpc-btn-bg2-active:rgba(255, 255, 255, 0.2);
    --tbpc-btn-border-color2-hover:rgba(255, 255, 255, 0.08);
    --tbpc-btn-border-color2-active:rgba(255, 255, 255, 0.08);
    --tbpc-img-bg: rgba(255, 255, 255, 0.08);
    --tbpc-search-bg-color: rgba(255, 255, 255, 0.08);
    --tbpc-non-vip-bg: rgba(255, 89, 13, 0.16);
    --tbpc-vip-color:#FFE5B2;
    --tbpc-vip-icon: url(https://img.alicdn.com/imgextra/i3/O1CN01piiqx71eKj6WuIvZU_!!6000000003853-2-tps-72-32.png);
    --tbpc-client-hover-color: #FF590D;
    --tbpc-client-active-color: #FFAD87;
    --tbpc-btn-bg-hover: linear-gradient(0deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.08)), rgba(255, 241, 235, 0.08);
    --tbpc-btn-bg-active: linear-gradient(0deg, rgba(255, 255, 255, 0.16), rgba(255, 255, 255, 0.16)), rgba(255, 241, 235, 0.08);
    --tbpc-btn-border-color-hover: rgba(255, 255, 255, 0.08);
    --tbpc-btn-border-color-active: rgba(255, 255, 255, 0.08);
    --tbpc-btn-bg-vip: rgba(255, 220, 173, 0.16);
    --tbpc-btn-border-color-vip: rgba(255, 255, 255, 0.08);
    --tbpc-btn-bg-hover-vip: linear-gradient(0deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.08)), rgba(255, 220, 173, 0.16);
    --tbpc-btn-bg-active-vip: linear-gradient(0deg, rgba(255, 255, 255, 0.16), rgba(255, 255, 255, 0.16)), rgba(255, 220, 173, 0.16);
    --tbpc-btn-border-color-hover-vip: rgba(255, 255, 255, 0.08);
    --tbpc-btn-border-color-active-vip: rgba(255, 255, 255, 0.08);
    --tbpc-search-tag-bg: rgba(255, 89, 13, 0.16);
    --tbpc-loading-bg1: rgba(255, 255, 255, 0.2);
    --tbpc-loading-bg2: rgba(255, 255, 255, 0.3);
    --tbpc-search-icon-bg-hover: #FF7536;
    --tbpc-search-icon-bg-active: #FF915E;
    --tbpc-my-tao-backup-icon: url(https://img.alicdn.com/imgextra/i2/O1CN01uSaQ4l1cyucnLE9XM_!!6000000003670-2-tps-208-208.png);
    --bg-recommend-banner: #292929;
    --recommend-banner-border: rgba(0, 0, 0, 0.16);
    --recommend-banner-shadow: rgba(0, 0, 0, 0.24);

    --tbpc-home-more-box-shadow-color: rgba(173, 14, 54, 0.24);
    --tbpc-shopping-url: url(https://img.alicdn.com/imgextra/i2/O1CN01UKV8qx1kcUARlQRjk_!!6000000004704-2-tps-148-64.png);
    --tbpc-more-arrow-url: url(https://img.alicdn.com/imgextra/i3/O1CN014C166c1pliDWhYbAQ_!!6000000005401-2-tps-64-64.png);
    --tbpc-feedback-icon-image: url(https://img.alicdn.com/imgextra/i1/O1CN01WJ4h9H1XU9xPeBuUb_!!6000000002926-2-tps-96-96.png);
    --tbpc-feedback-bg-color: #292929;
    --tbpc-feedback-bg-color-hover: #3d3d3d;
    --tbpc-feedback-bg-color-active: #525252;
    --tbpc-feedback-shadow-color: rgba(0, 0, 0, 0.16);
    --tbpc-feedback-text-color: rgba(255, 255, 255, 0.96);
  }

  .business-entry-bbs-card {
    background-color: var(--bg-light-color, #f5f5f5) !important;
  }

  .business-entry-title-card-top-title {
    color: var(--primary-color, #1f1f1f) !important;
  }

  .business-entry-item-card {
    background-color: var(--bg-light-color, #f5f5f5) !important;
  }

  .business-entry-live-card {
    background-color: var(--bg-light-color, #f5f5f5) !important;
  }

  .client-tao-coin-wrapper .ClientHomeTaocoinEnter {
    background-color: var(--bg-light-color, #f5f5f5) !important;
  }
  .client-tao-coin-wrapper .client-home-taocoin-enter-title {
    color: var(--bg-white-color, rgba(255,255,255, 0.96)) !important;
  }
  .client-tao-coin-wrapper .client-home-taocoin-enter-taocoin {
    color: var(--tb-brand-light-color, #ff5000) !important;
  }
  .client-tao-coin-wrapper .client-home-taocoin-enter-info-desc {
    color: var(--tb-brand-light-color, #ff5000) !important;
  }
  .client-tao-coin-wrapper .client-home-taocoin-enter-action {
    background: var(--tb-brand-light-color, #ff5000) !important;
  }
  .client-tao-coin-wrapper .mask {
    background: var(--tbpc-loading-bg1, rgba(0, 0, 0, 0.03)) !important;
  }
}

blockquote,
body,
button,
dd,
dl,
dt,
fieldset,
form,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
input,
legend,
li,
ol,
p,
pre,
td,
textarea,
th,
ul {
  margin: 0;
  padding: 0;
}

body,
button,
input,
select,
textarea {
  font: 12px/1.5 system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, BlinkMacSystemFont, Helvetica Neue, Arial, PingFang SC, PingFang TC, PingFang HK, Microsoft Yahei, Microsoft JhengHei;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
}

address,
cite,
dfn,
em,
var {
  font-style: normal;
}

code,
kbd,
pre,
samp {
  font-family: courier new, courier, monospace;
}

small {
  font-size: 12px;
}

ol,
ul {
  list-style: none;
}

a {
  color: inherit;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  text-decoration: none !important;
}

a:focus {
  outline: none;
}

sup {
  vertical-align: text-top;
}

sub {
  vertical-align: text-bottom;
}

legend {
  color: #000;
}

fieldset,
img {
  border: 0;
}

button,
input,
select,
textarea {
  font-size: 100%;
}

button {
  border-radius: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

@font-face {
  font-family: 'iconfont';
  /* Project id 403341 */
  src: url('//at.alicdn.com/t/a/font_403341_dn4j9o0i5di.woff2?t=1746513825269') format('woff2'),
    url('//at.alicdn.com/t/a/font_403341_dn4j9o0i5di.woff?t=1746513825269') format('woff'),
    url('//at.alicdn.com/t/a/font_403341_dn4j9o0i5di.ttf?t=1746513825269') format('truetype');
}

.tb-ifont {
  font-family: 'iconfont' !important;
  font-size: 14px;
  font-style: normal;
  display: inline-block;
  text-decoration: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#ice-container .search-suggest {
  background: var(--bg-color, #fff) !important;
}
#ice-container [data-sg-type=placeholder] {
  color: var(--tab-color, #11192d) !important;
}

#ice-container .search-suggest-out-wrapper {
  position: relative;
  width: 896px !important;
  height: 56px !important;
}

#ice-container .search-suggest-out-wrapper #J_Search {
  width: 896px !important;
  height: 56px !important;
}

#ice-container .search-suggest-out-wrapper #J_Search [data-sg-type="hotWord"] {
  max-width: 896px;
  margin-top: 16px;
  height: 32px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
}

@media screen and (max-width: 911px) {
  #layout-search-bar {
    width: 816px !important;
  }

  #ice-container .search-suggest-out-wrapper {
    position: relative;
    width: 816px !important;
  }

  #ice-container .search-suggest-out-wrapper #J_Search {
    width: 816px !important;
  }

  #ice-container .search-suggest-out-wrapper #J_Search [data-sg-type="hotWord"] {
    max-width: 816px;
  }
}

#ice-container .search-suggest-out-wrapper #J_Search [data-sg-type="placeholder"] {
  left: 16px !important;
  top: 6px;
}

#ice-container .search-suggest-out-wrapper #J_Search .btn-search {
  width: 40px;
  height: 40px;
  top: 6px;
  right: 6px;
  color: transparent;
  background-image: url("https://img.alicdn.com/imgextra/i2/O1CN01JpFBRP28A8NqUBATN_!!6000000007891-2-tps-48-48.png");
  background-position: center;
  background-size: 24px 24px;
  background-repeat: no-repeat;
  background-color: var(--tb-brand-light-color, #FF5000);
}

#ice-container .search-suggest-out-wrapper #J_Search .btn-search:hover {
  background-color: var(--tbpc-search-icon-bg-hover, #FF7536);
}

#ice-container .search-suggest-out-wrapper #J_Search .btn-search:active {
  background-color: var(--tbpc-search-icon-bg-active, #FF915E);
}

#ice-container .search-suggest-out-wrapper #J_Search .search-suggest-combobox {
  margin-left: 6px !important;
  top: 4px;
}

#ice-container .search-suggest-out-wrapper #J_Search [data-sg-type="hotWord"] .item {
  font-size: 12px;
  padding: 0 12px;
  height: 32px;
  line-height: 32px;
  margin-right: 16px;
  border-radius: 8px;
  background: var(--bg-color, #fff);
  box-sizing: border-box;
  border: 1px solid var(--tbpc-border-color, rgba(0, 0, 0, 0.08));
  color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92)) !important;
  font-weight: 400 !important;
}

#ice-container .search-suggest-out-wrapper #J_Search [data-sg-type="hotWord"] .item:hover {
  background: var(--tbpc-bg-color, rgba(0, 0, 0, 0.04));
  border: 1px solid var(--tbpc-border-color, rgba(0, 0, 0, 0.08));
  font-weight: 400 !important;
}

#ice-container .search-suggest-out-wrapper #J_Search [data-sg-type="hotWord"] .item:active {
  background: var(--tbpc-border-color, rgba(0, 0, 0, 0.08));
  border: 1px solid var(--tbpc-border-color, rgba(0, 0, 0, 0.08));
}

/* #ice-container
  .search-suggest-out-wrapper
  #J_Search
  [data-sg-type="hotWord"]
  .item:first-child {
  padding: 0;
}
#ice-container
  .search-suggest-out-wrapper
  #J_Search
  [data-sg-type="hotWord"]
  .item:not(:first-child) {
  background: #f7f7f7;
  border-radius: 8px;
} */

/* #ice-container .search-suggest-image-search-out-icon {
  position: absolute;
  min-width: 40px;
  width: 40px;
  height: 40px;
  background: url("https://img.alicdn.com/imgextra/i2/O1CN01bBhAhw1ZPEtOU036B_!!6000000003186-2-tps-80-80.png") no-repeat center/cover;
  top: 8px;
  right: 58px;
}

#ice-container .search-suggest-image-search-out-icon .image-search-icon-outerMode {
  opacity: 0;
} */

#ice-container .search-suggest-split,
#ice-container [data-sg-type="tab"] {
  display: none;
}

#ice-container .search-suggest-popup {
  top: 52px !important;
}

#ice-container .search-suggest-popup .item-text {
  padding-left: 14px !important;
  color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92)) !important;
}
#ice-container  .search-suggest .search-suggest-menu-item .item-text b {
  color: var(--secondary-color, #7a7a7a) !important;
}

#ice-container .search-suggest-popup .search-suggest-menu {
  background: var(--bg-color, #fff);
}

#ice-container .search-suggest-popup .search-suggest-menu .search-suggest-menu-item .text {
  color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92)) !important;
}
body .search-suggest .search-suggest-menu-item.active, body .search-suggest .search-suggest-menu-item:hover {
  background-color: var(--tbpc-search-bg-color, #f3f6f8) !important;
}

#ice-container .search-suggest-popup .search-suggest-menu .search-suggest-menu-history .search-suggest-menu-history-item {
  background: var(--bg-color, #fff);
  box-sizing: border-box;
  border: 1px solid var(--tbpc-border-color, rgba(0, 0, 0, 0.08));
  color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92)) !important;
}

#ice-container .search-suggest-popup .search-suggest-menu .search-suggest-menu-history .search-suggest-menu-history-header .search-suggest-menu-history-title {
  color: var(--secondary-color, #7a7a7a) !important;
}
#ice-container .search-suggest-menu .search-suggest-menu-history .search-suggest-menu-history-header .search-suggest-menu-history-clean-text:hover {
  color: var(--tb-brand-light-color, #ff5000) !important;
}
#ice-container .search-suggest-menu .search-suggest-menu-history .search-suggest-menu-history-header .search-suggest-menu-history-clean-text {
  color: var(--secondary-color, #7a7a7a) !important;
}
#ice-container .search-suggest-menu .search-suggest-menu-content .titleWrapper .title{
  color: var(--secondary-color, #7a7a7a) !important;
}
#ice-container .search-suggest-combobox>input, body [data-sg-type=combobox]>input{
  color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92)) !important;
  background: var(--bg-color, #fff) !important;
}
#ice-container .placeholderItemWrapper .iconTabText {
  color: var(--tb-brand-light-color, #ff5000) !important;
}

#ice-container .placeholderItemWrapper .iconTabWrapper {
  background: var(--tbpc-search-tag-bg, rgba(255, 80, 0, 0.082)) !important;
}
#ice-container .search-suggest-menu .search-suggest-menu-history .search-suggest-menu-history-item:hover {
  border: 1px solid var(--tb-brand-light-color, #ff5000) !important;
  color: var(--tb-brand-light-color, #ff5000) !important;
}
