import { obj2String } from '.';


interface IGoldLogRecordOptions {
  logKey: string;
  gmKey: 'EXP' | 'CLK';
  goKey: Record<string, any>;
}

export function goldlogRecord(options: IGoldLogRecordOptions) {
  const { logKey, gmKey, goKey } = options || {};

  if (!logKey) {
    return;
  }

  const resGmKey = String(gmKey).toUpperCase() === 'EXP' ? 'EXP' : 'CLK';

  const resGoKey = obj2String({
    ...(goKey || {}),
  });


  window?.goldlog?.record(logKey, resGmKey, resGoKey, 'GET');
}
