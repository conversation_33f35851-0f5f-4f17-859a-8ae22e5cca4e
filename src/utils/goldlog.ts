import { obj2String } from '.';
import { getPageSpm } from './spm';


interface IGoldLogRecordOptions {
  logKey: string;
  gmKey: 'EXP' | 'CLK';
  goKey: Record<string, any>;
}

export function goldlogRecord(options: IGoldLogRecordOptions) {
  const { logKey, gmKey, goKey } = options || {};

  if (!logKey) {
    return;
  }

  const resGmKey = String(gmKey).toUpperCase() === 'EXP' ? 'EXP' : 'CLK';

  const resGoKey = obj2String({
    ...(goKey || {}),
  });


  window?.goldlog?.record(logKey, resGmKey, resGoKey, 'GET');
}


// 购物车卡片曝光
export const goldLogShoppingCartExp = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.shoppingcart',
    gmKey: 'EXP',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};

// 购物车卡片整体点击
export const goldLogShoppingCartClk = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.shoppingcart',
    gmKey: 'CLK',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};

// 购物车卡片商品区域点击
export const goldLogShoppingCartProductClk = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.shoppingcart.product',
    gmKey: 'CLK',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};