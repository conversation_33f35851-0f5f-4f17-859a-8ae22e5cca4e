export const $staticConfig = {
    search: {
        showImageSearch: false,
        dataSetId: 40375079,
        currentAldResId: '34795268',
        showImageSearchTip: false,
    },
    usercenter: {
        externalEntranceBackup: [],
        currentAldResId: '34793293',
        contentId: '17223449264430001',
        showNotLoginImg: false,
        distinctId: '17223449264430001',
        dataSetId: 43144829,
        functionalAreaBackup: [
            {
                distinctId: '17225211315070001',
                dataSetId: 41103875,
                currentAldResId: '34793296',
                contentId: '17225211315070001',
                icon: '&#xe62c;',
                link: 'https://i.taobao.com/my_itaobao/itao-tool/collect',
                text: '宝贝收藏',
                id: '17225211315070001',
                __pos__: 1,
                __track__: '34793296.34793296.45771135.2.1',
            },
            {
                distinctId: '17225211315240001',
                dataSetId: 41103875,
                currentAldResId: '34793296',
                icon: '&#xe62d;',
                link: 'https://favorite.taobao.com/list_bought_shops_n.htm',
                text: '买过的店',
                id: '17225211315240001',
                __pos__: 2,
                __track__: '34793296.34793296.45771135.2.2',
            },
            {
                distinctId: '17225211315290001',
                dataSetId: 41103875,
                currentAldResId: '34793296',
                contentId: '17225211315290001',
                icon: '&#xe631;',
                link: 'https://i.taobao.com/my_itaobao/subscription',
                text: '收藏的店',
                id: '17225211315290001',
                __pos__: 3,
                __track__: '34793296.34793296.45771135.2.3',
            },
            {
                distinctId: '17225211315330001',
                dataSetId: 41103875,
                currentAldResId: '34793296',
                contentId: '17225211315330001',
                icon: '&#xe634;',
                link: 'https://i.taobao.com/my_itaobao/itao-tool/footMark',
                text: '我的足迹',
                id: '17225211315330001',
                __pos__: 4,
                __track__: '34793296.34793296.45771135.2.4',
            },
        ],
        id: '17223449264430001',
        __pos__: 1,
        __track__: '34793293.34793293.47484765.2.1',
        benefitSliderBackup: [
            {
                distinctId: '17389122007530001',
                dataSetId: 44115099,
                currentAldResId: '36687825',
                contentId: '17389122007530001',
                id: '17389122007530001',
                targetUrl:
                    'https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-561441&disableNav=YES&status_bar_transparent=true',
                itemImgUrl:
                    'https://img.alicdn.com/imgextra/i2/O1CN01cl7SRg1JvGkjs4smB_!!6000000001090-0-tps-448-112.jpg',
                __pos__: 1,
                __track__: '36687825.36687825.48324677.2.1',
            },
        ],
        externalEntranceBackupIcon: [],
    },
    bannerBackupNew: [
        {
            backgroundColor: '#EE3E00',
            subtitle1: '甄选实惠好物',
            currentAldResId: '35510782',
            contentId: '17287319823610001',
            title1: '良心好货',
            title2: '国民工厂',
            topLogo: 'https://img.alicdn.com/imgextra/i4/O1CN01kmQfN922anihKtzlk_!!6000000007137-2-tps-262-64.png',
            jumpUrl: 'https://web.m.taobao.com/app/tgc-pc/shop/home',
            itemImage: 'https://img.alicdn.com/imgextra/i2/O1CN01eollzL1mFhcf72cF1_!!6000000004925-2-tps-480-384.png',
            distinctId: '17287319823610001',
            titleColor: '#ffffff',
            dataSetId: 42588574,
            id: '17287319823610001',
            __pos__: 1,
            __track__: '35510782.35510782.47035510.2.1',
        },
        {
            backgroundColor: '#d61216',
            subtitle1: '好物5折起',
            currentAldResId: '35510782',
            contentId: '17287319823520001',
            title1: '百亿秒杀节',
            title2: '限时秒杀',
            topLogo: 'https://img.alicdn.com/imgextra/i1/O1CN01K6Jefq1XHnC2SKLms_!!6000000002899-2-tps-669-64.png',
            jumpUrl: 'https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-554070',
            itemImage: 'https://img.alicdn.com/imgextra/i4/O1CN01lz0znS1i88cKtOGBW_!!6000000004367-2-tps-480-384.png',
            distinctId: '17287319823520001',
            titleColor: '#ffffff',
            dataSetId: 42588574,
            id: '17287319823520001',
            __pos__: 2,
            __track__: '35510782.35510782.47035510.2.2',
        },
        {
            backgroundColor: '#EE2929',
            subtitle1: '网罗全网低价',
            currentAldResId: '35510782',
            contentId: '17287319823370001',
            title1: '淘宝秒杀',
            title2: '天天好价',
            topLogo: 'https://img.alicdn.com/imgextra/i4/O1CN01jUoVhQ1ng640sgH1E_!!6000000005118-2-tps-277-64.png',
            jumpUrl: 'https://web.m.taobao.com/app/ltao-fe/tbmx-pc-page/home',
            itemImage: 'https://img.alicdn.com/imgextra/i3/O1CN01dIhzeJ1vMPyBoUirT_!!6000000006158-2-tps-480-384.png',
            distinctId: '17287319823370001',
            titleColor: '#ffffff',
            dataSetId: 42588574,
            id: '17287319823370001',
            __pos__: 3,
            __track__: '35510782.35510782.47035510.2.3',
        },
        {
            backgroundColor: '#00a452',
            subtitle1: '就选淘宝买菜',
            currentAldResId: '35510782',
            contentId: '17287319823570001',
            title1: '新鲜果蔬',
            title2: '产地优选',
            topLogo: 'https://img.alicdn.com/imgextra/i3/O1CN01wIgr3y1bH74Jsf8Ly_!!6000000003439-2-tps-604-64.png',
            jumpUrl: 'https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-551926&disableNav=YES&status_bar_transparent=true',
            itemImage: 'https://img.alicdn.com/imgextra/i2/O1CN01EHgfb61IwM28twhw1_!!6000000000957-2-tps-480-384.png',
            distinctId: '17287319823570001',
            titleColor: '#ffffff',
            dataSetId: 42588574,
            id: '17287319823570001',
            __pos__: 4,
            __track__: '35510782.35510782.47035510.2.4',
        },
        {
            backgroundColor: '#89270b',
            subtitle1: '爆款好物精选',
            currentAldResId: '35510782',
            contentId: '17287319823470001',
            title1: '工厂裸价',
            title2: '大牌平替',
            topLogo: 'https://img.alicdn.com/imgextra/i4/O1CN01Fpyj7T20kIhg3EhGo_!!6000000006887-2-tps-279-64.png',
            jumpUrl: 'https://huodong.taobao.com/wow/a/act/tao/dailygroup/23509/24308/wupr?wh_pid=daily-551927&disableNav=YES&status_bar_transparent=true',
            itemImage: 'https://img.alicdn.com/imgextra/i4/O1CN01DO5qgj1whJewoO1hp_!!6000000006339-2-tps-480-384.png',
            distinctId: '17287319823470001',
            titleColor: '#ffffff',
            dataSetId: 42588574,
            id: '17287319823470001',
            __pos__: 5,
            __track__: '35510782.35510782.47035510.2.5',
        },
        {
            backgroundColor: '/',
            subtitle1: '防寒保暖必备',
            backgroundImage: 'https://img.alicdn.com/imgextra/i2/O1CN01yuAhED1x2qH3eEY3S_!!6000000006386-0-tps-1040-384.jpg',
            currentAldResId: '35510782',
            contentId: '17287319823420001',
            title1: '秋风萧瑟',
            title2: '换季无忧',
            topLogo: 'https://img.alicdn.com/imgextra/i1/O1CN01tDELZu1ROlWshzkYI_!!6000000002102-2-tps-297-64.png',
            jumpUrl: 'https://huodong.taobao.com/wow/a/act/tmall/dailygroup/21201/21856/wupr?wh_pid=daily-540365&disableNav=YES&status_bar_transparent=true',
            itemImage: '/',
            distinctId: '17287319823420001',
            titleColor: '#000000',
            dataSetId: 42588574,
            id: '17287319823420001',
            __pos__: 6,
            __track__: '35510782.35510782.47035510.2.6',
        },
    ],
    cacheAbility: {
        useCacheFirst: true,
        useLSForCacheFirst: true,
        useLocalStorage: true,
        useIndexDB: true,
        stopSetCache: false,
    },
};
