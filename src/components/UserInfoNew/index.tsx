import { useEffect, useMemo, useState } from 'react';
import './index.less';
import dayjs from 'dayjs';
import { filter, keys, size, values } from 'lodash-es';
import { queryMultiResources } from '@/utils/ald-request';
import { HomeUrlHref, isTrue, transferStringToFix2 } from '@/utils/index';
import { getAvatar, getNick, isLogin } from '@/utils/cookie';
import { getHello, goldLogUserExp, reportLog } from './utils';
// import ExternalLink from './components/ExternalLink';
// import MyTao from './components/MyTao';
import UserLogout from './components/UserLogout';
// import UserSlider from './components/UserSlider';
import {
  queryBag,
  queryBenefitCount,
  // queryBenefitRemind,
  // queryCouponRemind,
  queryOrderListCount,
  queryOrderStatus,
  // queryRedEnvelopeUseUrl,
  queryTaoCoinCount,
} from './services';
import {
  BENEFIT_AD_DATA,
  DEFAULT_BENEFIT_COUNT,
  DEFAULT_BENEFIT_RED_PACKET_JUMP_URL,
  DEFAULT_BENEFIT_COUPON_JUMP_URL,
  DEFAULT_ORDER_COUNT,
  DefaultBottomColor,
  DefaultTopColor,
  DEFAULT_ORDER_DATA,
  DEFAULT_BENEFIT_JUMP_URL,
} from './constant';
import type {
  IAvatarConfig,
  IBenefitRemoteSlider,
  IBenefitBottomSlider,
  ILoginAreaConfig,
  ILoginSliderRes,
  IOrderCountRes,
  IOrderSliderItem,
  IQueryLowPriceData,
  IQueryOrderListCountRes,
  ISubSection,
  IUserInfo,
  ILoginSliderItem,
} from './types';
import { LOGIN_STATUS, SLIDER_ITEM_TYPE } from './types';
import useEvoContext from '../../hooks/useEvoContext';
import { IEvoContextValue } from '../../contexts/EvoContext';
import { allSettled } from '@/utils/promise-all-settled';
import { getValueFromEvoData } from '@/utils/evo';
import AdaptiveGrid, { MediumBlock } from '../AdaptiveGrid';
import OrderCount from './components/OrderCount';
import { getPageSpm } from '@/utils/spm';
import LoginCard from '../LoginCard';
import { read } from 'fs';

const LoginSliderMap = {
  // 登录【中间slider降价提醒】
  LoginLowPriceSlider: 33718930,
} as const;

function UserInfo() {
  const { components } = useEvoContext('homeRight') as IEvoContextValue;

  // 是否加载骨架屏
  const [isLoading, setIsLoading] = useState(true);
  // 是否登录
  const [login, setLogin] = useState<LOGIN_STATUS>(LOGIN_STATUS.NONE);
  const [loginAreaConfig, setLoginAreaConfig] = useState<ILoginAreaConfig>({
    topColor: DefaultTopColor,
    bottomColor: DefaultBottomColor,
  });
  // 昵称/头像/问候词
  const [userInfo] = useState<IUserInfo>({
    nick: getNick(),
    avatar: getAvatar(),
    helloText: isLogin() ? `${getHello()}，` : `${getHello()}`,
  });
  // 头像框
  const [avatarConfig, setAvatarConfig] = useState<IAvatarConfig>();
  // 购物车/待收货/待发货/待付款/待评价数量
  const [orderCount, setOrderCount] =
    useState<IOrderCountRes>(DEFAULT_ORDER_COUNT);
  // 降价/待付款/物流信息
  const [orderData, setOrderData] = useState<{
    isFetching: boolean;
    data: IOrderSliderItem[];
  }>({
    isFetching: true,
    data: DEFAULT_ORDER_DATA,
  });
  // // 营销位置数据
  // const [businessEntry, setBusinessEntry] = useState<IBusinessEntry>();
  // const [functionalArea, setFunctionalArea] = useState<IFunctionalArea[]>([]);
  // 个人专属权益
  const [benefitCount, setBenefitCount] = useState({
    isFetching: true,
    ...DEFAULT_BENEFIT_COUNT,
  });
  const [benefitSliderList, setBenefitSliderList] = useState<
    (IBenefitBottomSlider | IBenefitRemoteSlider)[]
  >([]);
  // 权益轮播数据请求状态
  const [isBenefitSliderLoading, setIsBenefitSliderLoading] = useState(true);
  // 是否强登
  // const noStrongLogin =
  //   login === LOGIN_STATUS.NONE || login === LOGIN_STATUS.WEAK;

  useMemo(() => {
    if (isTrue(isLogin())) {
      setLogin(LOGIN_STATUS.WEAK);
    }
    if (typeof window?.isLoginFromLibPromise?.then === 'function') {
      window.isLoginFromLibPromise.then((res) => {
        if (res && res?.data) {
          setLogin(LOGIN_STATUS.STRONG);
        } else {
          setLogin(LOGIN_STATUS.NONE);
        }
      });
    }
  }, []);

  useEffect(() => {
    queryLoginArea();
  }, []);

  // 进行个人中心所有的请求
  async function queryLoginArea() {
    try {
      const _promiseList: any[] = [
        queryBag(),
        queryOrderListCount(),
      ] as const;
      const [bagRes, orderCountRes] = await allSettled(_promiseList);
      // console.log('bagRes, orderCountRes=', bagRes, orderCountRes);
      setIsLoading(false);
      let resLoginAreaConfig: ILoginAreaConfig = {};
      let resAvatarConfig: IAvatarConfig = {};
      let remindsList: IBenefitRemoteSlider[] = [];
      let noticesList: ILoginSliderItem[] = [];
      const configRes = getValueFromEvoData({
        components,
        componentCode: 'userInfo',
        blockCode: 'userInfoConfig',
      });
      // console.log('configRes=', configRes);
      if (configRes) {
        // 背景色处理
        resLoginAreaConfig = configRes?.[0] || {};
        // console.log('configRes==', resLoginAreaConfig);
        if (resLoginAreaConfig?.topColor && resLoginAreaConfig?.bottomColor) {
          setLoginAreaConfig({
            topColor: resLoginAreaConfig.topColor,
            bottomColor: resLoginAreaConfig.bottomColor,
          });
        }
        // 头像框处理
        resAvatarConfig = configRes?.[0] || {};
      }
      const remindsRes = getValueFromEvoData({
        components,
        componentCode: 'assetInfo',
        blockCode: 'reminds',
      });
      // console.log('remindsRes=', remindsRes);
      if (remindsRes) {
        remindsList = remindsRes || [];
      }
      const noticeRes = getValueFromEvoData({
        components,
        componentCode: 'noticeInfo',
        blockCode: 'notices',
      });
      // console.log('noticeRes=', noticeRes);
      if (noticeRes) {
        noticesList = noticeRes || [];
      }
      // console.log('bagRes', bagRes, bagRes?.status);
      if (bagRes?.status === 'fulfilled') {
        goldLogUserExp('profile', 'nick');
        setLogin(LOGIN_STATUS.STRONG);
        // 处理头像框
        avatarRender(resAvatarConfig);
        // 处理购物车数量
        const cartNo = bagRes.value?.count || 0;
        if (orderCountRes?.status === 'fulfilled') {
          orderCountRender(orderCountRes?.value, cartNo);
        }
        // 处理营销入口
        benefitSliderRender({
          remindsList: remindsList,
        });
        // 待收货/待发货/待付款/待评价
        orderRender({ noticesList: noticesList });
        // 红包/优惠券/淘金币
        benefitCountRender();
      } else {
        benefitSliderRender({
          remindsList: [],
        });
        orderRender({ noticesList: [] });
        setBenefitCount({
          isFetching: false,
          ...DEFAULT_BENEFIT_COUNT,
        });
      }
    } catch (e) {
      benefitSliderRender({
        remindsList: [],
      });
      orderRender({ noticesList: [] });
      setBenefitCount({
        isFetching: false,
        ...DEFAULT_BENEFIT_COUNT,
      });
      setIsLoading(false);
      reportLog(`主接口异常, error: ${JSON.stringify(e)}`);
    }
  }

  // 轮播区域
  async function orderRender({
    noticesList,
  }: {
    noticesList: ILoginSliderItem[];
  }) {
    try {
      const [resp, aldResLowPrice] = await allSettled([
        queryOrderStatus(),
        queryMultiResources<{
          [LoginSliderMap.LoginLowPriceSlider]: IQueryLowPriceData;
        }>({
          resIdList: [LoginSliderMap.LoginLowPriceSlider],
        }),
      ]);
      // 1. 处理待付款/已揽件
      const sections =
        resp.status === 'rejected'
          ? undefined
          : resp.value?.containers?.entrance_home_main_pc?.base?.sections;
      const resSubSection =
        filter(sections, { sectionBizCode: 'pcUserInfo' })[0]?.subSection || {};
      // 2. 处理降价通知
      const resLowPrice =
        aldResLowPrice.status === 'rejected'
          ? undefined
          : aldResLowPrice.value.data?.[LoginSliderMap.LoginLowPriceSlider];
      // console.log('aldResLowPrice, resLowPrice=', aldResLowPrice, resLowPrice);
      const resLoginAdv =
        size(noticesList) > 0
          ? ({
            success: true,
            data: noticesList,
            bottom: false,
          } as unknown as ILoginSliderRes)
          : ({} as ILoginSliderRes);
      // console.log('resSubSection, resLowPrice, resLoginAdv=', resSubSection, resLowPrice, resLoginAdv);
      orderListShow(resSubSection, resLowPrice, resLoginAdv);
    } catch (error) {
      reportLog('数据不存在');
      setOrderData({
        isFetching: false,
        data: DEFAULT_ORDER_DATA,
      });
    }
  }

  function getBenefitCount<T>(
    response: { value?: { success: boolean; data: T } },
    key: keyof T,
    defaultCount: { success: boolean; num: number },
    shouldConvertToFix2: boolean = false,
  ) {
    return isTrue(response?.value?.success)
      ? {
        success: true,
        num: shouldConvertToFix2
          ? transferStringToFix2(
            (response?.value?.data?.[key] || 0) as string,
          )
          : response?.value?.data?.[key] || 0,
      }
      : defaultCount;
  }

  // 红包/优惠券/淘金币
  async function benefitCountRender() {
    try {
      const [redEnvelopeRes, taoCoinRes] = await allSettled([
        queryBenefitCount(),
        queryTaoCoinCount(),
      ]);
      const resBenefitCount = {
        isFetching: false,
        redEnvelop: getBenefitCount(
          redEnvelopeRes,
          'redEnvelope',
          DEFAULT_BENEFIT_COUNT.redEnvelop,
        ),
        coupon: getBenefitCount(
          redEnvelopeRes,
          'coupon',
          DEFAULT_BENEFIT_COUNT.coupon,
        ),
        taoCoin: getBenefitCount(
          taoCoinRes,
          'coinSaving',
          DEFAULT_BENEFIT_COUNT.taoCoin,
          true,
        ),
      };
      setBenefitCount(resBenefitCount);
    } catch (e) {
      setBenefitCount({
        isFetching: false,
        ...DEFAULT_BENEFIT_COUNT,
      });
    }
  }

  function avatarRender(resAvatarConfig: IAvatarConfig) {
    const { bgColor: resBgColor, iconUrl: resIconUrl } = resAvatarConfig;
    if (resBgColor && resIconUrl) {
      setAvatarConfig({
        bgColor: resBgColor,
        iconUrl: resIconUrl,
      });
    }
  }

  function orderCountRender(
    orderCountRes: IQueryOrderListCountRes,
    cartNo: number,
  ) {
    const resOrderCount = { ...DEFAULT_ORDER_COUNT };
    if (Array.isArray(orderCountRes?.result)) {
      orderCountRes?.result?.forEach((item) => {
        if (item.tabCode) {
          resOrderCount[item.tabCode] = item.count || 0;
        }
      });
    }
    resOrderCount.cart = cartNo;
    setOrderCount(resOrderCount);
  }

  // 轮播区域数据处理
  function orderListShow(
    subSection?: ISubSection,
    lowPrice?: IQueryLowPriceData,
    loginAdv?: ILoginSliderRes,
  ) {
    let _orderData: IOrderSliderItem[] = [];
    const subKeyList = keys(subSection);
    if (subKeyList.includes('waitPaymentInfo')) {
      const waitPaymentInfoList: IOrderSliderItem[] = values(
        subSection?.waitPaymentInfo?.item,
      ).map(item => ({
        ...(item || {}),
        smartContent: {
          ...(item?.smartContent || {}),
          scheduledCloseTime: dayjs(item.smartContent?.scheduledCloseTime).format('MM.DD HH:mm'),
        },
        _type: SLIDER_ITEM_TYPE.WaitPaymentInfo,
      }));
      _orderData = _orderData.concat(waitPaymentInfoList);
    }
    if (subKeyList.includes('logisticsDeliveryInfo')) {
      const logisticsDeliveryList: IOrderSliderItem[] = Object.values(
        subSection?.logisticsDeliveryInfo?.item,
      ).map(item => ({
        ...(item as IOrderSliderItem || {}),
        _type: SLIDER_ITEM_TYPE.LogisticsDeliveryInfo,
      }));
      _orderData = _orderData.concat(logisticsDeliveryList);
    }
    // 因为个人中心不展示购物车消息， 所以不需要降价提醒
    // if (size(lowPrice?.data) > 0) {
    //   const lowPriceList = map(lowPrice?.data, (lowPriceItem) => {
    //     return {
    //       ...lowPriceItem,
    //       targetUrl: lowPrice?.cartUrl || '',
    //       _type: SLIDER_ITEM_TYPE.LowPrice,
    //       smartContent: {
    //         itemImgUrl: lowPriceItem.url,
    //         statusName: lowPrice?.title || '降价提醒',
    //         subTitle: lowPrice?.subTitle || '您有购物车商品已降价',
    //       },
    //     };
    //   });
    //   _orderData = _orderData.concat(lowPriceList);
    // }
    // 不需要广告位
    // if (size(loginAdv?.data) > 0) {
    //   const advList = map(loginAdv?.data, (adItem) => {
    //     return {
    //       ...adItem,
    //       targetUrl: adItem.targetUrl,
    //       _type: SLIDER_ITEM_TYPE.AdvInfo,
    //       smartContent: {
    //         itemImgUrl: adItem.itemImgUrl,
    //         statusName: '',
    //         subTitle: '',
    //       },
    //     };
    //   });
    //   _orderData = _orderData.concat(advList);
    // }
    if (size(_orderData) > 0) {
      setOrderData({
        isFetching: false,
        data: _orderData,
      });
    } else {
      setOrderData({
        isFetching: false,
        data: DEFAULT_ORDER_DATA,
      });
    }
  }

  function generateBenefitUrl(slider: IBenefitRemoteSlider) {
    if (slider.jumpUrl) {
      return slider.jumpUrl;
    }
    return slider?.assetType === 'coupon'
      ? slider?.subAssetType === 'zk'
        ? DEFAULT_BENEFIT_JUMP_URL
        : `${DEFAULT_BENEFIT_COUPON_JUMP_URL}&g_couponId=${slider?.extraData?.templateCode || ''}&g_couponGroupId=${slider?.extraData?.couponTag || ''}`
      : DEFAULT_BENEFIT_RED_PACKET_JUMP_URL;
  }

  // 登录后权益模块兜底
  async function benefitSliderRender(params: {
    remindsList: IBenefitRemoteSlider[];
  }) {
    const { remindsList } = params;
    try {
      if (remindsList.length > 0) {
        const benefitListWithUrl = [...remindsList].map((slider) => {
          if (
            slider?.assetType &&
            ['coupon', 'fund'].includes(slider.assetType)
          ) {
            return {
              ...slider,
              targetUrl: generateBenefitUrl(slider),
              type: slider?.assetType === 'coupon' ? 'coupon' : 'fund',
            };
          } else {
            return {
              ...BENEFIT_AD_DATA[0],
              ...slider,
              type: 'ad',
            };
          }
        });
        // 先展示红包再展示卡券
        setBenefitSliderList(benefitListWithUrl);
        setIsBenefitSliderLoading(false);
      } else {
        let resBenefitAdList: IBenefitBottomSlider[] = [];
        resBenefitAdList = BENEFIT_AD_DATA;
        const adSlidersWithType = resBenefitAdList.map((slider) => ({
          ...slider,
          type: 'ad',
        }));
        setBenefitSliderList(adSlidersWithType);
        setIsBenefitSliderLoading(false);
      }
    } catch (e) {
      setIsBenefitSliderLoading(false);
    }
  }

  if(isBenefitSliderLoading){
    return null;
  }

  return login === LOGIN_STATUS.NONE ? <LoginCard helloText={userInfo?.helloText} /> :
    <div className="new-user-wrapper" data-spm="profile">
      <UserLogout
        login={login}
        loginAreaConfig={loginAreaConfig}
        userInfo={userInfo}
        avatarConfig={avatarConfig}
        showOrder
        benefitSliderList={benefitSliderList}
        benefitCount={benefitCount}
      />
    </div>
}

export default UserInfo;
