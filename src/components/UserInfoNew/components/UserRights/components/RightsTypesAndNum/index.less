.rights-types-and-num-container {
  box-sizing: border-box;
  padding: 12px 16px 8px;
  width: 100%;
  height: 64px;
  .rights-types-and-num {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 9;
    .right-item {
      width: 50%;
      padding: 0 10px;
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      box-sizing: border-box;
      justify-content: center;
      align-items: center;
      color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
      .right-item-detail {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: flex-end;
        height: 24px;
        max-width: 100%;
        overflow: hidden;
      }
      .right-item-label {
        font-size: 16px;
        font-weight: 600;
        line-height: 18px;
        font-family: "Inter V", sans-serif;
        margin-right: 2px;
      }

      .right-item-coupon-label {
        font-size: 16px;
        font-weight: 600;
        line-height: 18px;
      }

      .right-item-amount {
        font-size: 24px;
        font-weight: 600;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
        font-family: "Inter V", sans-serif;
      }

      .right-item-desc {
        font-size: 12px;
        line-height: 16px;
        text-align: center;
        margin-top: 4px;
      }
    }

    .right-item:hover {
      .right-item-desc,
      .right-item-detail {
        color: var(--tbpc-client-hover-color, #ff5000);
      }
    }

    .right-item:active {
      .right-item-desc,
      .right-item-detail {
        color: var(--tbpc-client-active-color, #FFAD87);
      }
    }

    .right-item-skeleton {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 69.33px;
      @keyframes loadingAnimation {
        0%,
        100% {
          background: rgba(0, 0, 0, 0.03);
        }
        50% {
          background: rgba(0, 0, 0, 0.06);
        }
      }
      .skeletonLoading {
        animation: loadingAnimation 2s ease-in-out infinite;
      }
      .right-item-detail-skeleton {
        width: 48px;
        height: 24px;
        margin: 0 auto;

        border-radius: 4px;
        background: rgba(0, 0, 0, 0.03);
      }

      .right-item-desc-skeleton {
        width: 24px;
        height: 16px;
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.03);
        display: block;
        margin-top: 4px;
      }
    }
  }
}
