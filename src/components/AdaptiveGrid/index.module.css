.gridContainer {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin: 0 auto;
  margin-bottom: 16px;
}

.smallBlock {
  background: var(--bg-light-color, #f5f5f5);
  border-radius: 12px;
  height: 192px;
  width: 100%;
  min-width: 208px;
}

.mediumBlock {
  background: var(--bg-light-color, #f5f5f5);
  border-radius: 12px;
  height: 192px;
  width: 100%;
  min-width: 360px;
  grid-column: span 2;
}

@media screen and (max-width: 911px) {
  .gridContainer {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .smallBlock, .mediumBlock {
    grid-column: span 1;
    height: 192px;
    min-width: 360px;
  }
}