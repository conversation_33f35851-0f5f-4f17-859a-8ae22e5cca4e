import React from 'react';
import styles from './index.module.css';

interface LoginCardProps {
  helloText?: string;
}

const LoginCard = ({ helloText = '' }: LoginCardProps) => {

  const handleLogin = () => {
    if (window && window.lib && window.lib.login && window.lib.login.goLoginAsync) {
      window.lib.login.goLoginAsync();
    }
  };

  return (
    <div className={styles.loginCard}>
      {/* 头部区域 */}
      <div className={styles.cardHeader}>
        <div className={styles.greetingSection}>
          <img 
            src="https://img.alicdn.com/imgextra/i4/6000000003354/O1CN011Ziokf1aeBTuBRtxg_!!6000000003354-2-gg_dtc.png"
            alt="问候图标"
            className={styles.greetingIcon}
          />
          <span className={styles.greetingText}>
            {helloText}
          </span>
        </div>
      </div>

      {/* 内容区域 */}
      <div className={styles.cardContent}>
        <div className={styles.contentText}>
          <div className={styles.mainText}>
           登录后更多精彩
          </div>
          <div className={styles.subText}>
            更懂你的推荐，更便捷的搜索
          </div>
        </div>
      </div>

      {/* 按钮区域 */}
      <div className={styles.cardFooter}>
        <button 
          onClick={handleLogin}
          className={styles.loginButton}
        >
          立即登录
        </button>
      </div>
    </div>
  );
};

export default LoginCard;
