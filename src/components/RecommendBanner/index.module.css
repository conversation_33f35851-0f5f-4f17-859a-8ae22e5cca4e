.bannerContainer {
  border-radius: 56px;
  background: linear-gradient(90deg, rgba(210, 76, 255, 0.8) 0%, rgba(250, 62, 62, 0.8) 50%, rgba(245, 82, 0, 0.8) 100%);
  box-shadow: 0 8px 16px 0 var(--tbpc-home-more-box-shadow-color, rgba(173, 14, 54, 0.16));
  width: 344px;
  height: 56px;
  cursor: pointer;
  margin: auto;
  position: relative;
  transition: width 0.2s ease;
}

.bannerContent {
  border-radius: inherit;
  background: var(--bg-color, #fff);
  position: absolute;
  inset: 1px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  box-sizing: border-box;
  padding: 6px 20px;
}

.carouselImages {
  width: 64px;
  height: 32px;
  border-radius: 16px;
  position: relative;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  margin-left: 12px;
  flex-shrink: 0;
}

.carouselImages div {
  position: absolute;
  left: 0;

  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 16px;
  flex-shrink: 0;
  transition: all 0.2s linear;
  background-size: cover;
}

.textContainer {
  flex: 1;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  margin-left: 8px;
}

.bannerText {
  width: 74px;
  height: 32px;
  background-image: var(--tbpc-shopping-url, url(https://img.alicdn.com/imgextra/i2/O1CN01UKV8qx1kcUARlQRjk_!!6000000004704-2-tps-148-64.png));
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  flex-shrink: 0;
}

.bannerTextMore {
  min-width: 88px;
  margin-left: 4px;
  font-size: 14px;
  line-height: 1;
  height: 32px;
  color: var(--tb-brand-light-color, #ff5000);
  white-space: nowrap;
  text-align: center;
  flex: 1;

  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.arrowIcon {
  /* margin-left: 20px; */
  position: relative;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--tb-brand-light-color, #ff5000);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.arrowIcon::after {
  position: absolute;
  content: "";
  font-size: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01UOFS541RvHj11fNdD_!!6000000002173-2-tps-64-64.png);
}

.heatDesc {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  color: var(--desc-color, rgba(0, 0, 0, 0.52));
  margin-left: 4px;
}

.slider {
  flex: 1;
  display: flex;
  text-align: center;
  flex-direction: column;
  width: 100px;
}

.typewriter {
  display: inline-block;
  font-family: monospace;
  position: relative;
  white-space: nowrap;
  /* 20 是字符数，3s 是动画时长 */
  /* 需根据内容修改 steps(20, end) 的 20 */
  animation: typewriter-clip 1.8s steps(var(--steps), end) forwards;
}

@keyframes typewriter-clip {
  from {
    clip-path: inset(0 100% 0 0);
  }

  to {
    clip-path: inset(0 0 0 0);
  }
}