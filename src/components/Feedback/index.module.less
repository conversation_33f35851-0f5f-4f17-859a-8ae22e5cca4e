.container {
  border-radius: 12px;
  background-color: var(--tbpc-feedback-bg-color, #fff);
  border: 1px solid var(--recommend-banner-border, rgba(0, 0, 0, 0.02));
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  box-sizing: border-box;
  padding: 8px 0;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  box-shadow: 0 2px 6px 0 var(--tbpc-feedback-shadow-color, rgba(0, 0, 0, 0.08));
  position: fixed;
  bottom: 16px;
  right: 16px;
  z-index: 9;
  cursor: pointer;
  &:hover {
    background-color: var(--tbpc-feedback-bg-color-hover, #f5f5f5);
  }
  &:active {
    background-color: var(--tbpc-feedback-bg-color-active, #ebebeb);
  }
}

.iconImage {
  background: var(--tbpc-feedback-icon-image, url(https://img.alicdn.com/imgextra/i1/O1CN01dGQxnE1WutShXPR2J_!!6000000002849-2-tps-96-96.png));
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
}

.textImage {
  font-family: PingFang SC;
  font-size: 10px;
  font-weight: normal;
  line-height: 14px;
  height: 14px;
  text-align: center;
  color: var(--tbpc-feedback-text-color, #1f1f1f);
  margin-top: 2px;
}
