.business-entry-title-card-top {
  display: flex;
  overflow: hidden;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;

  width: 100%;
  height: 24px;
  padding-top: 12px;
  padding-bottom: 12px;
}

.business-entry-title-card-top-title-container {
  display: flex;
  overflow: hidden;
  align-items: center;
  flex: 1;
  flex-direction: row;
  flex-wrap: wrap;

  height: 24px;
}

.business-entry-title-card-top-title-rtl{
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: wrap;
  justify-content: flex-end;

  margin-left: 16px;
}

.business-entry-title-card-top-title-tag-left {
  display: flex;
  align-items: center;
  flex-direction: row;

  box-sizing: border-box;
  margin-right: 4px;
  img {
    object-fit: contain;
  }
}

.business-entry-title-card-top-title {
  flex-shrink: 0;

  max-width: 100%;

  color: var(--primary-color, #1f1f1f);

  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
}

.business-entry-title-card-top-title-tag {
  box-sizing: border-box;
  margin-left: 8px;

  color: var(--tbpc-white-color, #fff);
  border-radius: 4px;

  font-size: 12px;
  font-weight: 500;
  line-height: 0;

  img {
    object-fit: contain;
  }
}

.business-entry-title-card-top-image {
  width: 8px;
  margin-right: 16px;

  line-height: 8px;

  img {
    width: 8px;
    height: 8px;
    margin-top: 4px;
  }

  span {
    width: 8px;
    height: 8px;
    margin-top: 4px;

    opacity: 0.5;

    font-size: 8px;
  }
}