import AdaptiveGrid, { SmallBlock } from '@/components/AdaptiveGrid';
import styles from '../../pages/home.module.css';
import logo from '../../assets/logo.png';

function Placeholder() {
  return (
    <div className={styles.app}>
      <div className={styles.header} />
      <div className={styles.logoSection}>
        <div className={styles.logoWrapper}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            fill="currentColor"
            width="106"
            height="56"
            viewBox="0 0 106.02005767822266 56"
            className={styles.logo}
          >
            <path
              d="M97.7401,7.82607C93.217,6.53084,82.9555,6.74363,74.9807,9.22572C69.48,10.9378,65.0375,13.0049,59.7931,16.2804L58.2228,15.0906C60.3298,11.5814,61.5377,7.45948,62.4359,2.97865L69.0713,4.33938C67.7897,6.90291,66.921,8.24838,65.985,9.35963C69.7152,7.33689,73.9895,5.5427,78.718,4.3236L78.4458,0.184665L86.1434,0L85.9049,3.02203C91.0299,2.50798,96.8443,2.747,100.416,4.10619C105.408,6.00634,107.049,10.2243,103.664,14.9419L100.523,14.4444C101.893,11.0563,101.506,8.90457,97.7401,7.82607ZM21.3967,5.52781C25.3366,3.38469,31.0979,2.05929,35.4911,2.20726C44.9707,2.52686,51.5006,5.70939,51.5149,21.1343C51.3439,33.5898,47.9677,40.4289,37.9227,39.9791C35.6937,39.8795,34.011,39.5419,31.8437,38.9826L32.3065,36.3755C39.8611,37.9092,42.7962,35.8863,44.3046,28.1271C45.2092,23.4745,45.1712,17.9674,44.5054,13.8149C43.7657,9.20277,40.6997,6.57544,35.0511,6.45679C27.4118,6.29613,20.0278,8.49154,13.6216,13.2839L11.8049,11.6696C14.0543,9.1659,16.3749,3.93269,17.0463,0.0416828L23.7251,1.04971C23.364,2.23572,22.3986,4.32928,21.3967,5.52781ZM7.2017,10.1758C9.39546,10.1758,11.1739,8.40132,11.1739,6.21243C11.1739,4.02354,9.39546,2.24907,7.2017,2.24907C5.00793,2.24907,3.22951,4.02354,3.22951,6.21243C3.22951,8.40132,5.00793,10.1758,7.2017,10.1758ZM37.1875,33.816C39.8317,33.6125,41.1107,32.1606,40.9863,30.3518L40.6601,25.6131C37.8579,27.1729,34.859,28.4637,32.0136,29.4221C32.054,27.2552,32.082,25.0984,32.0995,22.9477C34.4253,22.8776,36.7321,22.8014,38.9914,22.7226C41.0452,22.6507,41.9643,21.8003,42.0364,19.2432C38.7466,19.3672,35.424,19.49,32.1177,19.598C32.1217,18.3203,32.1226,17.0439,32.12,15.768C34.278,15.701,36.246,15.6273,37.7032,15.5549C39.7559,15.4531,40.5835,14.7456,40.7483,12.0758C35.6918,12.2808,29.6585,12.4756,24.4284,12.5423C24.9066,11.955,25.3627,11.3196,25.7925,10.6272L21.1025,9.88704C19.7759,12.0043,17.7852,14.2247,15.6614,16.123L17.4357,18.0067C18.5489,17.4125,19.6446,16.7649,20.6964,16.0129C22.2912,15.9981,24.1682,15.9669,26.1176,15.9251C26.1271,17.1675,26.1331,18.4581,26.1352,19.7744C22.9029,19.8583,19.7159,19.9192,16.6229,19.9433L16.4046,23.3031C19.587,23.2608,22.8552,23.1948,26.1321,23.113C26.1226,25.7705,26.0963,28.4527,26.0499,30.9838C25.8202,31.0243,25.5933,31.062,25.3713,31.0954C22.6177,31.5105,21.3046,31.0061,21.245,29.3412C21.2195,28.63,21.3666,27.7162,21.6455,26.4824L16.4672,25.8591C15.9544,27.5275,15.7712,29.431,15.8561,30.8098C16.1128,34.9828,20.1778,36.557,27.3511,34.7433C29.8155,34.1202,34.3124,32.2403,36.9567,30.8031L37.1875,33.816ZM85.6042,34.3357C85.6465,31.7372,85.6764,28.773,85.6819,25.8159C90.3362,25.6976,94.5256,25.5606,97.2074,25.4207C99.2013,25.3723,100.286,24.3779,100.31,21.5997C96.0898,21.7574,90.9457,21.9318,85.6749,22.0806C85.664,20.2825,85.6419,18.5493,85.6053,16.9703C89.3919,16.8717,92.8159,16.7629,95.3564,16.6646C97.3501,16.6231,98.4939,15.6357,98.5513,12.8575C88.6856,13.1483,77.0991,13.471,67.3121,13.5515L67.3121,17.2767C70.8633,17.2608,74.8318,17.2047,78.7433,17.1276C78.777,18.6932,78.8016,20.4363,78.8164,22.256C74.042,22.3642,69.4498,22.436,65.6462,22.439L65.6462,26.1643C69.365,26.1343,74.084,26.0647,78.8324,25.9702C78.8341,28.8969,78.8107,31.8459,78.7592,34.4359C72.0344,34.5151,65.4476,34.5339,59.5995,34.4684L59.5995,38.4248C73.4097,38.5021,89.1248,38.2291,102.132,37.8355C104.457,37.8024,105.727,36.7919,106.02,33.8172C99.7551,34.03,92.7157,34.2119,85.6042,34.3357ZM2.64022,17.2203L5.23868,13.3482C9.9565,15.8457,11.782,17.3395,12.9227,19.1098C13.6082,20.1736,13.5147,21.7922,13.0976,23.4048C12.3384,26.3409,9.97815,32.1434,7.05542,39.0888L0,35.7338C5.50847,29.4329,8.92388,24.3317,9.44216,23.1684C9.99585,21.9261,9.78602,21.1268,8.70066,20.3871C7.78576,19.7636,5.57463,18.4888,2.64022,17.2203ZM92.4044,29.6565C92.4044,31.442,93.8549,32.9069,95.6441,32.9287C97.4334,32.9505,98.8839,31.521,98.8839,29.7357C98.8839,27.9505,97.4334,26.4855,95.6441,26.4635C93.8549,26.4418,92.4044,27.8713,92.4044,29.6565ZM38.2544,46.7783C37.06,46.7528,36.2061,47.1532,35.6261,47.6318L35.6148,44L33.4631,44L33.4631,55.8925C34.1476,55.8313,34.8721,55.463,35.3517,54.9811C36.1636,55.7576,36.9976,55.9741,38.0229,55.9741C40.2553,55.9741,42.321,54.2188,42.321,51.4701C42.321,48.5188,40.4989,46.8261,38.2544,46.7783ZM13.0668,44.4516L13.0723,44.3849L2.5,44.3766L2.5,46.1192L6.48531,46.1192L6.48711,55.8922L8.83809,55.8922L8.83613,46.1192C9.43992,46.1227,10.6862,46.1206,11.2579,46.1196C11.4116,46.1193,11.5161,46.1192,11.5469,46.1192C12.5011,46.1199,12.9649,45.581,13.0668,44.4516ZM20.3512,50.4815Q20.3413,51.5902,20.3208,53.4589C20.3106,54.6089,20.6396,54.9145,21.3382,54.8497C21.1561,55.1542,20.5769,55.8922,19.6036,55.8922C18.8199,55.8922,18.3225,55.3498,18.1563,55.0556C17.4911,55.6199,16.6096,55.9745,15.5255,55.9745C13.7746,55.9745,12.1825,54.7495,12.2019,52.5258C12.2189,50.59,13.7655,49.4048,15.587,49.312C16.802,49.2217,17.772,49.6145,18.2211,50.1074C18.2763,49.0105,17.914,48.5569,17.2472,48.2825C16.4601,47.9586,14.6224,48.0277,13.0837,48.5016L13.296,47.0895Q14.6791,46.778,15.9543,46.778C16.9869,46.778,17.8266,46.8634,18.4482,47.1373C19.4138,47.5628,20.3765,48.4991,20.3512,50.4815ZM26.96,46.778C24.0055,46.778,22.308,48.7296,22.308,51.3734C22.308,54.017,24.0055,55.9745,26.96,55.9745C29.9145,55.9745,31.6119,54.017,31.6119,51.3734C31.6119,48.7296,29.9145,46.778,26.96,46.778ZM51.9863,53.4589Q52.0067,51.5902,52.0168,50.4815C52.0419,48.4991,51.0794,47.5628,50.1138,47.1373C49.4922,46.8634,48.6523,46.778,47.6198,46.778Q46.3445,46.778,44.9614,47.0895L44.7493,48.5016C46.2879,48.0277,48.1257,47.9586,48.9128,48.2825C49.5794,48.5569,49.9418,49.0105,49.8865,50.1074C49.4376,49.6145,48.4674,49.2217,47.2525,49.312C45.431,49.4048,43.8844,50.59,43.8675,52.5258C43.8479,54.7495,45.44,55.9745,47.1911,55.9745C48.275,55.9745,49.1567,55.6199,49.8218,55.0556C49.9881,55.3498,50.4853,55.8922,51.2691,55.8922C52.2424,55.8922,52.8216,55.1542,53.0036,54.8497C52.3052,54.9145,51.9762,54.6089,51.9863,53.4589ZM58.6014,46.778C55.6469,46.778,53.9496,48.7296,53.9496,51.3734C53.9496,54.017,55.6469,55.9745,58.6014,55.9745C61.5559,55.9745,63.2533,54.017,63.2533,51.3734C63.2533,48.7296,61.5559,46.778,58.6014,46.778ZM26.9618,54.6388L26.9518,54.6388C25.5009,54.6388,24.6912,53.3419,24.6912,51.3734C24.6912,49.4046,25.5009,48.1749,26.9518,48.1749L26.9618,48.1749C28.4127,48.1749,29.2226,49.4046,29.2226,51.3734C29.2226,53.3419,28.4127,54.6388,26.9618,54.6388ZM58.6031,54.6388L58.5932,54.6388C57.1424,54.6388,56.3325,53.3419,56.3325,51.3734C56.3325,49.4046,57.1424,48.1749,58.5932,48.1749L58.6031,48.1749C60.0541,48.1749,60.864,49.4046,60.864,51.3734C60.864,53.3419,60.0541,54.6388,58.6031,54.6388ZM37.8145,54.5762L37.8243,54.5762C39.2807,54.5762,40.1169,53.3208,40.1169,51.4139C40.1169,49.5076,39.2807,48.3166,37.8243,48.3166L37.8145,48.3166C37.2333,48.3166,36.753,48.5067,36.3879,48.8568C35.847,49.3209,35.5643,50.123,35.5476,51.2312L35.5476,51.2372C35.5458,51.2954,35.545,51.3547,35.545,51.4139C35.545,51.6209,35.5546,51.8195,35.5739,52.0107C35.6846,53.3386,36.2369,54.5762,37.8145,54.5762ZM16.3131,54.6227C15.1553,54.6227,14.3766,53.7099,14.3867,52.5839C14.3968,51.4578,15.192,50.5449,16.3497,50.5449C17.5075,50.5449,18.2864,51.4578,18.2763,52.5839C18.2662,53.7099,17.4708,54.6227,16.3131,54.6227ZM47.9785,54.6227C46.8208,54.6227,46.0419,53.7099,46.052,52.5839C46.0621,51.4578,46.8573,50.5449,48.0152,50.5449C49.1728,50.5449,49.9517,51.4578,49.9416,52.5839C49.9315,53.7099,49.1363,54.6227,47.9785,54.6227ZM77.567,51.3733C77.567,48.7296,79.2642,46.778,82.2187,46.778C85.1732,46.778,86.8706,48.7296,86.8706,51.3733C86.8706,54.0169,85.1732,55.9744,82.2187,55.9744C79.2642,55.9744,77.567,54.0169,77.567,51.3733ZM82.2106,54.6388L82.2205,54.6388C83.6714,54.6388,84.4813,53.3419,84.4813,51.3734C84.4813,49.4046,83.6714,48.1749,82.2205,48.1749L82.2106,48.1749C80.7598,48.1749,79.9499,49.4046,79.9499,51.3734C79.9499,53.3419,80.7598,54.6388,82.2106,54.6388ZM73.008,48.175L73.0179,48.175C73.9423,48.175,74.6066,48.6742,74.9669,49.5457L76.4689,48.8134C75.8378,47.4506,74.7939,46.7778,73.0161,46.7778C70.0616,46.7778,68.3642,48.7326,68.3642,51.3762C68.3642,54.0198,70.0616,55.9743,73.0161,55.9743C74.7939,55.9743,75.8378,55.3017,76.4689,53.9389L74.9669,53.2066C74.6066,54.0782,73.9423,54.5774,73.0179,54.5774L73.008,54.5774C71.5571,54.5774,70.7472,53.3449,70.7472,51.3762C70.7472,49.4075,71.5571,48.175,73.008,48.175ZM65.5967,53.2932C64.8476,53.2841,64.2403,53.8825,64.2403,54.63C64.2403,55.3774,64.8476,55.9908,65.5967,55.9999C66.3458,56.009,66.9531,55.4106,66.9531,54.6631C66.9531,53.9157,66.3458,53.3023,65.5967,53.2932ZM101.919,53.4565Q101.95,51.7062,101.95,50.5975C101.95,48.736,101.643,47.9507,101.105,47.4978C100.845,47.2536,100.521,47.0732,100.134,46.9639C99.843,46.8815,99.5368,46.833,99.2342,46.8089C98.2502,46.7302,97.3296,46.9047,96.5481,47.5562C96.4034,47.6767,96.3004,47.7802,96.1191,47.971C96.0864,48.0056,96.0461,48.0518,96.0024,48.1042C95.8761,47.8443,95.72,47.6518,95.5375,47.4978C95.2771,47.2536,94.9532,47.0732,94.5667,46.9639C94.275,46.8815,93.9688,46.833,93.6663,46.8089C92.6822,46.7302,91.7616,46.9047,90.9801,47.5562C90.8554,47.6601,90.7615,47.7515,90.6218,47.8971C90.5991,47.8597,90.5751,47.823,90.5478,47.7878C90.0991,47.2133,89.3725,46.9506,88.6878,46.8894L88.6878,55.912L90.8396,55.912L90.8396,49.7822C90.8396,49.1916,91.1544,48.6306,91.6837,48.3664C91.9524,48.2324,92.243,48.1766,92.5248,48.1777C92.8275,48.179,93.2315,48.2443,93.514,48.4416C93.8901,48.7041,94.202,49.0205,94.2499,49.7931L94.2499,55.9085L96.4016,55.9085L96.4016,49.7847C96.4016,49.1976,96.7134,48.6412,97.2372,48.3736C97.5099,48.2343,97.806,48.1764,98.0927,48.1777C98.3954,48.179,98.7994,48.2443,99.082,48.4416C99.5213,48.7485,99.8746,49.1265,99.8195,50.2234L99.795,54.038L99.795,54.7641C99.795,55.3517,100.234,55.5927,100.526,55.737C100.565,55.7567,100.607,55.7726,100.649,55.7874C100.802,55.8411,101.057,55.8911,101.219,55.8896C102.182,55.8811,102.755,55.15,102.936,54.8472C102.238,54.9121,101.909,54.6065,101.919,53.4565Z"
              fillRule="evenodd"
              fill="#FF590D"
              fillOpacity="1"
            />
          </svg>
        </div>
        <div className={styles.searchContainer}>
          <div
            id="layout-search-bar"
            style={{
              borderRadius: '12px',
              boxShadow: 'inset 0 0 0 2px #FF5000',
              display: 'flex',
              flexDirection: 'row',
              flexWrap: 'nowrap',
              overflow: 'hidden',
              width: '896px',
              height: '56px',
              position: 'relative',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'nowrap',
                boxSizing: 'border-box',
                padding: '17px 16px',
                alignItems: 'center',
                width: '792px',
                height: '100%',
              }}
            />
            <div
              style={{
                width: '40px',
                height: '40px',
                top: '8px',
                right: '8px',
                position: 'absolute',
                borderRadius: '8px',
                color: 'transparent',
                backgroundImage: 'url("https://img.alicdn.com/imgextra/i2/O1CN01JpFBRP28A8NqUBATN_!!6000000007891-2-tps-48-48.png")',
                backgroundPosition: 'center',
                backgroundSize: '24px 24px',
                backgroundRepeat: 'no-repeat',
                backgroundColor: 'var(--tb-brand-light-color, #ff5000)',
              }}
            />
          </div>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'center',
              overflow: 'hidden',
              width: '800px',
              height: '32px',
              marginTop: '16px',
            }}
          />
        </div>
      </div>

      <div className={styles.middleHeader} />
      <AdaptiveGrid>
        <SmallBlock />
        <SmallBlock />
        <SmallBlock />
        <SmallBlock />
      </AdaptiveGrid>
    </div>
  );
}

export default Placeholder;
